import Vue from "vue";
import VueRouter from "vue-router";

Vue.use(VueRouter);

const routes = [
  {
    path: "/",
    name: "Home",
    component: () => import("@/views/Home.vue"),
  },
  {
    path: "/priceScreen",
    name: "priceScreen",
    component: () => import("@/views/priceScreen.vue"),
  },
  {
    path: "/specialistScreen",
    name: "specialistScreen",
    component: () => import("@/views/specialistScreen.vue"),
  },
];

const router = new VueRouter({
  mode: "history",
  base: process.env.BASE_URL,
  routes,
});

export default router;
