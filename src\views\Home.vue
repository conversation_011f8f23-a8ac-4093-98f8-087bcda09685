<template>
  <div class="home">
    <div class="navigation">
      <router-link to="/priceScreen" class="nav-button">
        <div class="button-content">
          <h3>物价屏</h3>
        </div>
      </router-link>

      <router-link to="/specialistScreen" class="nav-button">
        <div class="button-content">
          <h3>专家出诊屏</h3>
        </div>
      </router-link>
    </div>
  </div>
</template>

<script>
export default {
  name: "Home",
};
</script>

<style lang="scss" scoped>
.home {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  color: white;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
  box-sizing: border-box;
}

.header {
  text-align: center;
  margin-bottom: 60px;
}

.header h1 {
  font-size: 56px;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.header p {
  font-size: 24px;
  opacity: 0.9;
}

.navigation {
  display: flex;
  gap: 40px;
  flex-wrap: wrap;
  justify-content: center;
}

.nav-button {
  display: block;
  width: 300px;
  height: 200px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 15px;
  text-decoration: none;
  color: white;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.nav-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.6);
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.button-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 20px;
}

.button-content h3 {
  font-size: 32px;
}

.button-content p {
  font-size: 18px;
  opacity: 0.8;
}
</style>
