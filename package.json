{"name": "hospital", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"autofit.js": "^3.2.8", "axios": "^1.11.0", "core-js": "^3.6.5", "element-ui": "^2.15.14", "normalize.css": "^8.0.1", "vue": "^2.6.11", "vue-router": "^3.2.0", "xml2js": "^0.6.2"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.19", "@vue/cli-plugin-eslint": "~4.5.19", "@vue/cli-plugin-router": "~4.5.19", "@vue/cli-service": "~4.5.19", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "node-sass": "^4.12.0", "sass-loader": "^8.0.2", "vue-template-compiler": "^2.6.11"}}