import axios from "axios";
import { API_CONFIG, SOAP_CONFIG } from "./config.js";
import xml2js from "xml2js";

// 创建axios实例
const soapClient = axios.create({
  baseURL: API_CONFIG.baseURL,
  timeout: API_CONFIG.timeout,
  headers: SOAP_CONFIG.headers,
  // 根据环境配置SSL证书验证
  httpsAgent: new (require("https").Agent)({
    rejectUnauthorized: API_CONFIG.rejectUnauthorized,
  }),
});

/**
 * 构建SOAP XML请求体
 * @param {string} action - 操作类型 (如: MES0061)
 * @param {string} message - 请求消息内容 (XML格式)
 * @returns {string} 完整的SOAP XML字符串
 */
function buildSoapXML(action, message) {
  // 修复：移除XML声明，与Postman保持一致
  return `<soap:Envelope xmlns:soap="${SOAP_CONFIG.envelope.soap}">
    <soap:Body>
        <HIPMessageServer xmlns="${SOAP_CONFIG.namespace}">
            <action>${action}</action>
            <message>
                <![CDATA[${message}]]>
            </message>
        </HIPMessageServer>
    </soap:Body>
</soap:Envelope>`;
}

/**
 * 解析SOAP响应，提取实际数据
 * @param {string} soapResponse - SOAP XML响应字符串
 * @returns {Promise<Object>} 解析后的JSON对象
 */
async function parseSoapResponse(soapResponse) {
  try {
    // 使用正则表达式提取CDATA中的内容
    const cdataMatch = soapResponse.match(/<!\[CDATA\[(.*?)\]\]>/s);
    if (cdataMatch && cdataMatch[1]) {
      const xmlContent = cdataMatch[1].trim();

      // 如果内容是XML格式，尝试解析为JSON
      if (xmlContent.startsWith("<")) {
        return await parseXMLToJSON(xmlContent);
      }

      // 如果已经是JSON格式，直接解析
      try {
        return JSON.parse(xmlContent);
      } catch (e) {
        return { data: xmlContent };
      }
    }

    // 如果没有找到CDATA，返回原始响应
    return { rawResponse: soapResponse };
  } catch (error) {
    console.error("解析SOAP响应失败:", error);
    return { error: "响应解析失败", rawResponse: soapResponse };
  }
}

/**
 * 使用xml2js库解析XML为JSON
 * @param {string} xml - XML字符串
 * @returns {Promise<Object>} 解析后的JSON对象
 */
async function parseXMLToJSON(xml) {
  try {
    // 配置xml2js解析器选项
    const parser = new xml2js.Parser({
      // 去除属性前缀
      attrkey: "attributes",
      // 将属性作为子元素处理
      mergeAttrs: true,
      // 去除命名空间前缀
      tagNameProcessors: [xml2js.processors.stripPrefix],
      // 去除属性命名空间前缀
      attrNameProcessors: [xml2js.processors.stripPrefix],
      // 将单个子元素转换为数组（保持一致性）
      explicitArray: false,
      // 去除空白字符
      trim: true,
      // 规范化标签名（转为小写）
      normalizeTags: false,
      // 将文本节点转换为字符串
      explicitCharkey: false,
    });

    // 解析XML
    const result = await parser.parseStringPromise(xml);
    return result;
  } catch (error) {
    console.error("XML解析错误:", error);
    // 如果解析失败，尝试简单的文本处理
    return { data: xml, error: "XML解析失败: " + error.message };
  }
}

/**
 * 发送SOAP请求的通用方法
 * @param {string} action - 操作类型
 * @param {string} message - 请求消息
 * @param {Object} options - 额外配置选项
 * @returns {Promise<Object>} 返回解析后的响应数据
 */
export async function sendSoapRequest(action, message, options = {}) {
  try {
    console.log(`发送SOAP请求 - Action: ${action}`);
    console.log("请求消息:", message);

    // 构建SOAP XML
    const soapXML = buildSoapXML(action, message).trim();

    // 发送请求
    const response = await soapClient.post("", soapXML, {
      ...options,
      headers: {
        ...SOAP_CONFIG.headers,
        ...options.headers,
      },
    });

    console.log("SOAP响应状态:", response.status);
    console.log("SOAP响应数据:", response.data);

    // 解析响应
    const parsedData = await parseSoapResponse(response.data);

    return {
      success: true,
      data: parsedData,
      status: response.status,
      headers: response.headers,
    };
  } catch (error) {
    console.error("SOAP请求失败:", error);

    // 处理不同类型的错误
    if (error.response) {
      // 服务器响应了错误状态码
      return {
        success: false,
        error: "服务器响应错误",
        status: error.response.status,
        data: error.response.data,
        message: error.message,
      };
    } else if (error.request) {
      // 请求已发出但没有收到响应
      return {
        success: false,
        error: "网络连接错误",
        message: "无法连接到服务器，请检查网络连接",
      };
    } else {
      // 其他错误
      return {
        success: false,
        error: "请求配置错误",
        message: error.message,
      };
    }
  }
}

/**
 * 请求拦截器 - 可用于添加通用的请求处理逻辑
 */
soapClient.interceptors.request.use(
  config => {
    console.log("发送SOAP请求:", config.url);
    return config;
  },
  error => {
    console.error("请求拦截器错误:", error);
    return Promise.reject(error);
  }
);

/**
 * 响应拦截器 - 可用于添加通用的响应处理逻辑
 */
soapClient.interceptors.response.use(
  response => {
    console.log("收到SOAP响应:", response.status);
    return response;
  },
  error => {
    console.error("响应拦截器错误:", error);
    return Promise.reject(error);
  }
);

export default soapClient;
