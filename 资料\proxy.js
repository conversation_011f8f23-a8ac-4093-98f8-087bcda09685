const express = require("express");
const { createProxyMiddleware } = require("http-proxy-middleware");
const path = require("path");

const app = express();

// 全局CORS中间件 - 解决所有跨域问题
app.use((req, res, next) => {
  // 允许所有来源
  res.header("Access-Control-Allow-Origin", "*");
  // 允许的HTTP方法
  res.header("Access-Control-Allow-Methods", "GET,PUT,POST,DELETE,OPTIONS,PATCH,HEAD");
  // 允许的请求头
  res.header(
    "Access-Control-Allow-Headers",
    "Origin, X-Requested-With, Content-Type, Accept, Authorization, Cache-Control, Pragma, SOAPAction"
  );
  // 允许携带凭证
  res.header("Access-Control-Allow-Credentials", "true");
  // 预检请求的缓存时间
  res.header("Access-Control-Max-Age", "86400");

  // 处理预检请求
  if (req.method === "OPTIONS") {
    res.sendStatus(200);
  } else {
    next();
  }
});

// 提供静态文件服务（HTML文件）
app.use(express.static(__dirname));

// API代理配置 - 转发所有/api开头的请求到目标服务器
app.use(
  "/api",
  createProxyMiddleware({
    target: "https://**********:1443",
    changeOrigin: true, // 修改请求头中的host字段
    secure: false, // 如果目标服务器使用自签名证书，设置为false
    // 保持原始路径，不重写
    pathRewrite: {
      "^/api": "", // 移除/api前缀，直接转发到目标服务器
    },
    // 请求日志
    onProxyReq: (proxyReq, req, res) => {
      console.log(
        `[${new Date().toISOString()}] 代理请求: ${req.method} ${
          req.originalUrl
        } -> ${proxyReq.getHeader("host")}${proxyReq.path}`
      );

      // 确保请求头正确设置
      proxyReq.setHeader("Host", "**********:1443");
      proxyReq.setHeader("Origin", "https://**********:1443");
    },
    // 响应处理
    onProxyRes: (proxyRes, req, res) => {
      console.log(
        `[${new Date().toISOString()}] 代理响应: ${proxyRes.statusCode} ${req.originalUrl}`
      );

      // 移除目标服务器可能设置的CORS头，使用我们自己的
      delete proxyRes.headers["access-control-allow-origin"];
      delete proxyRes.headers["access-control-allow-methods"];
      delete proxyRes.headers["access-control-allow-headers"];
      delete proxyRes.headers["access-control-allow-credentials"];
    },
    // 错误处理
    onError: (err, req, res) => {
      const timestamp = new Date().toISOString();
      console.error(`[${timestamp}] 代理错误: ${err.message}`);
      console.error("请求URL:", req.originalUrl);

      console.error("目标地址: https://**********:1443");
      console.error("错误代码:", err.code);
      console.error("错误err:", err);
      console.error("请求req:", req);
      console.error("请求res:", err);

      if (!res.headersSent) {
        res.status(500).json({
          error: "代理服务器错误",
          message: err.message,
          timestamp: new Date().toISOString(),
        });
      }
    },
    // 超时设置
    timeout: 30000, // 30秒超时
    proxyTimeout: 30000,
  })
);

// 健康检查端点
app.get("/health", (req, res) => {
  res.json({
    status: "ok",
    timestamp: new Date().toISOString(),
    proxy_target: "https://**********:1443",
    message: "代理服务器运行正常",
  });
});

// 404处理
app.use("*", (req, res) => {
  console.log(`[${new Date().toISOString()}] 404: ${req.method} ${req.originalUrl}`);
  res.status(404).json({
    error: "未找到请求的资源",
    path: req.originalUrl,
    message: "请确保API路径以/api开头",
    timestamp: new Date().toISOString(),
  });
});

const PORT = process.env.PORT || 3100;
app.listen(PORT, "172.21.49.222", () => {
  console.log("=".repeat(60));
  console.log(`🚀 代理服务器启动成功！`);
  console.log(`📍 本地访问地址: http://172.21.49.222:${PORT}`);
  console.log(`🌐 网络访问地址: http://172.21.49.222:${PORT}`);
  console.log(`🎯 代理目标服务器: https://**********:1443`);
  console.log(`📋 API代理规则: /api/* -> https://**********:1443/*`);
  console.log(`🔧 健康检查: http://172.21.49.222:${PORT}/health`);
  console.log(`📄 测试页面: http://172.21.49.222:${PORT}/test.html`);
  console.log("=".repeat(60));
  console.log("✅ 跨域问题已解决，支持所有来源的请求");
  console.log("📝 所有/api开头的请求将被转发到目标服务器");
});
