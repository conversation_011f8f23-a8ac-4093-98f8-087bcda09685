

东华医为对外标准接口文档


东华医为
计费医保、医生站
2018年03月06日




版本历史
版本	作者	参与者	发布日期	修改摘要
V3.0	李东			初稿
V3.0		倪康	2018-03-09	增加建卡挂号部分接口说明
V3.0		熊旺	2018-04-23	挂号、预约支付信息按收费修改
V3.0		熊旺	2018-04-26	增加6001对账接口
V3.0		熊旺	2018-07-07	增加5101住院费用查询接口
Copyright @ 2017 DHC,Inc. All rights reserved
请不要给第三方传阅



目 录

1. 引言	5
1.1. 文档编写目的	5
2. 接口标准	5
2.1. 对接方式	5
2.2. 消息格式	5
3. 接口地址	5
4. 接口规范	5
4.1. 物价查询接口	5

1.引言
1.1.文档编写目的
该文档给出了东华医为HIS系统提供给第三方系统的接口开发规范，作为开发设计人员使用。
2.接口标准
功能说明	根据交互操做服务编码和具体的消息流进行相应的交互操作
通讯方式	webServcie+Xml
服务地址	
方法名	HIPMessageServer(入参1 action,入参2 message)
	1、action类型为字符串，服务编号传给该参数，总线服务部署好提供
2、message类型为标准消息请求流传给该参数，为下列接口列表中的入参


2.1.对接方式
采用WebService方式。
2.2.消息格式
采用XML格式。
请求报文是以Request为根节点的XML串，应答报文是以Response为根节点的XML串。
3.接口地址
接口类型	接口地址
Webservice	https://**********:1443/csp/hsb/DHC.Published.ServiceForHATM.BS.ServiceForHATM.CLS?WSDL=1
备注	调用前需要提供调用侧IP给东华，由东华在总线加入白名单后才可调用

4.接口规范
4.1.物价查询接口
4.1.1.物价查询（9100）
交易编码	9100
服务编号	MES0061
接口方法	
描    述	根据诊疗收费项目别名查询诊疗收费项目价格
请求消息名	见“请求信息”表
应答消息名	见“返回信息”表
服务提供者	HIS
传输格式	XML

*******.请求信息
<Request>
  <Alias>MR</Alias>
  <TradeCode>9100</TradeCode>
  <Page>1</Page>
  <PageSize>10</PageSize>
</Request>
字段	字段说明	类型	长度	空值	备注
TradeCode	交易代码	String	4	    否	
Alias	收费项目别名	String		    空值查全部
Page  页码
PageSize  每页条数

*******.应答信息
<Response>
	<ResultCode>0</ResultCode>
	<ErrorMsg>成功</ErrorMsg>
	<TarItemS>
		<TarItem>
			<SerialNo>1</SerialNo>
			<ItemDesc>美容义齿</ItemDesc>
			<ItemCode>310518004</ItemCode>
			<PricesNo/>
			<Uom>每牙</Uom>
			<Price>60</Price>
			<SpecialPrice/>
			<Factory/>
			<ContentDesc/>
			<ChargeStandard/>
			<InsureSign/>
			<RegistrationNo/>
			<RegExpDate/>
			<SpecInfo/>
			<TarSubCate>其他</TarSubCate>
		</TarItem>
		<TarItem>
			<SerialNo>2</SerialNo>
			<ItemDesc>美容义齿加收(特殊设计)</ItemDesc>
			<ItemCode>310518004-1</ItemCode>
			<PricesNo/>
			<Uom>每牙</Uom>
			<Price>18</Price>
			<SpecialPrice/>
			<Factory/>
			<ContentDesc/>
			<ChargeStandard/>
			<InsureSign/>
			<RegistrationNo/>
			<RegExpDate/>
			<SpecInfo/>
			<TarSubCate>其他</TarSubCate>
		</TarItem>
	</TarItemS>
  <Page>1</Page>
  <PageSize>50</PageSize>
  <TotalCount>100</TotalCount>
  <RecordCount>50</RecordCount>
  <TotalPages>2</TotalPages>
</Response>
字段	字段说明	类型	长度	空值	备注
TradeCode	交易代码	String	4	否	
ResultCode	结果标志	String		否	（0为成功，其他为失败）
ResultContent	错误信息	String		否	
TarItemS	集合				
TarItem	集合				
SerialNo	序号				
ItemDesc	收费项目描述				
ItemCode	收费项目编码				
PricesNo	物价编码				
Uom	收费项目单位				
Price	单价				
SpecialPrice					暂时无意义
Factory					暂时无意义
ContentDesc					暂时无意义
ChargeStandard	收费依据				
InsureSign	医保标识				
RegistrationNo					暂时无意义
RegExpDate					暂时无意义
SpecInfo	规格				
TarSubCate	项目分类				
TotalCount 总数
RecordCount 当前页条数
PageSize 每页条数
Page 当前页
TotalPages 总页数

