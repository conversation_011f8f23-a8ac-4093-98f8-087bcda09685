# 医院项目 SOAP API 封装

本项目为医院系统提供了完整的SOAP API HTTP请求封装，支持科室查询、医生查询、排班查询等功能。

## 📁 文件结构

```
src/api/
├── config.js          # API配置文件（简化版）
├── request.js         # 通用SOAP请求封装
├── department.js      # 科室查询API
├── doctor.js          # 医生查询API
├── schedule.js        # 排班查询API
├── price.js           # 物价查询API
├── index.js           # API统一入口
├── example.js         # 使用示例
├── test.js            # API测试套件
└── README.md          # 说明文档
```

## 🚀 快速开始

### 1. 安装依赖

```bash
npm install axios
```

### 2. 基本使用

```javascript
import { API } from '@/api'

// 科室查询
const deptResult = await API.department.getAllDepartments()
if (deptResult.success) {
  console.log('科室查询成功:', deptResult.data)
}

// 医生查询
const doctorResult = await API.doctor.queryDoctorsByDepartment('188')
if (doctorResult.success) {
  console.log('医生查询成功:', doctorResult.data)
}

// 排班查询
const scheduleResult = await API.schedule.queryTodaySchedule('188', '187')
if (scheduleResult.success) {
  console.log('排班查询成功:', scheduleResult.data)
}

// 物价查询
const priceResult = await API.price.queryPriceByAlias('MR')
if (priceResult.success) {
  console.log('物价查询成功:', priceResult.data)
}
```

## 📋 API 功能

### 科室查询 API

#### `queryDepartment(params)`
查询科室列表 (交易编码: 1012)

**参数:**
```javascript
{
  hospitalId: 'SGSDYRMYY',      // 医院唯一编号
  extUserId: 'NOVA001',         // 操作员代码
  departmentType: '',           // 科室类别 (可选)
  departmentCode: '',           // 科室代码 (可选)
  departmentGroupCode: ''       // 一级科室代码 (可选)
}
```

#### `getAllDepartments()`
获取所有科室列表

#### `queryDepartmentByType(departmentType)`
根据科室类型查询科室

#### `querySubDepartments(departmentGroupCode)`
根据一级科室代码查询下级科室

### 医生查询 API

#### `queryDoctor(params)`
查询医生列表 (交易编码: 1013)

**参数:**
```javascript
{
  hospitalId: 'SGSDYRMYY',      // 医院唯一编号
  extUserId: 'NOVA001',         // 操作员代码
  departmentCode: '188',        // 科室代码 (必填)
  doctorCode: ''                // 医生代码 (可选)
}
```

#### `queryDoctorsByDepartment(departmentCode)`
根据科室代码查询医生列表

#### `queryDoctorInfo(departmentCode, doctorCode)`
查询指定医生信息

### 排班查询 API

#### `querySchedule(params)`
查询排班记录 (交易编码: 1004)

**参数:**
```javascript
{
  hospitalId: 'SGSDYRMYY',      // 医院唯一编号
  extUserId: 'NOVA001',         // 操作员代码
  startDate: '2025-08-19',      // 开始日期 (必填)
  endDate: '2025-08-19',        // 结束日期 (必填)
  departmentCode: '188',        // 科室代码 (必填)
  doctorCode: '187',            // 医生代码 (可选)
  stopScheduleFlag: 'N',        // 查询排班标记
  rbasSessionCode: '01'         // 出诊时段代码 (可选)
}
```

#### `queryScheduleTimeInfo(params)`
查询医生号源分时信息 (交易编码: 10041)

#### `queryTodaySchedule(departmentCode, doctorCode)`
查询今日排班

#### `queryScheduleByDate(date, departmentCode, doctorCode)`
查询指定日期的排班

### 物价查询 API

#### `queryPrice(params)`
物价查询 (交易编码: 9100)

**参数:**
```javascript
{
  alias: 'MR',                      // 收费项目别名 (必填) 项目描述后首拼
  tradeCode: '9100'                 // 交易代码 (默认: 9100)
}
```

**返回数据结构:**
```javascript
{
  success: true,
  data: {
    ResultCode: '0',
    ErrorMsg: '成功',
    TarItemS: {
      TarItem: [
        {
          SerialNo: '1',
          ItemDesc: '美容义齿',
          ItemCode: '310518004',
          Uom: '每牙',
          Price: '60',
          TarSubCate: '其他'
        }
      ]
    }
  },
  message: '物价查询成功'
}
```

#### `queryPriceByAlias(alias)`
根据别名查询物价 (简化调用)

#### `queryMRPrice()`
查询MR相关物价

#### `queryCTPrice()`
查询CT相关物价

#### `queryDRPrice()`
查询DR相关物价

## 🔧 配置说明

### 环境配置

在 `config.js` 中可以配置不同环境的参数：

```javascript
const ENV_CONFIG = {
  development: {
    baseURL: 'https://**********:1443/...',
    timeout: 30000,
    rejectUnauthorized: false, // 开发环境忽略SSL
    enableLogging: true
  },
  production: {
    baseURL: 'https://**********:1443/...',
    timeout: 15000,
    rejectUnauthorized: true,  // 生产环境验证SSL
    enableLogging: false
  }
}
```

### SOAP 配置

```javascript
export const SOAP_CONFIG = {
  headers: {
    'Content-Type': 'text/xml; charset=utf-8',
    'SOAPAction': 'http://www.dhcc.com.cn/...'
  },
  namespace: 'http://www.dhcc.com.cn',
  envelope: {
    soap: 'http://schemas.xmlsoap.org/soap/envelope/'
  }
}
```

## 💡 在 Vue 组件中使用

```vue
<template>
  <div>
    <el-button @click="fetchPrice" :loading="loading">
      查询物价
    </el-button>
    <div v-if="priceData">
      {{ priceData }}
    </div>
  </div>
</template>

<script>
import { API } from '@/api'

export default {
  data() {
    return {
      priceData: null,
      loading: false
    }
  },
  methods: {
    async fetchPrice() {
      this.loading = true
      try {
        const result = await API.price.queryPrice('MR', '9100')
        if (result.success) {
          this.priceData = result.data
          this.$message.success('查询成功')
        } else {
          this.$message.error(result.message)
        }
      } catch (error) {
        this.$message.error('请求异常')
      } finally {
        this.loading = false
      }
    }
  }
}
</script>
```

## 🔍 自定义 SOAP 请求

如果需要发送自定义的SOAP请求：

```javascript
import { sendSoapRequest } from '@/api'

// 自定义请求消息
const customMessage = `
  <Request>
    <Alias>CUSTOM</Alias>
    <TradeCode>9999</TradeCode>
    <Extra>additional_data</Extra>
  </Request>
`

const result = await sendSoapRequest('MES0061', customMessage)
```

## 🛠️ 错误处理

API 封装提供了完善的错误处理机制：

```javascript
const result = await API.price.queryPrice('MR', '9100')

if (!result.success) {
  switch (result.error) {
    case '服务器响应错误':
      console.log('服务器状态码:', result.status)
      break
    case '网络连接错误':
      console.log('网络连接失败')
      break
    case '请求配置错误':
      console.log('请求参数错误')
      break
  }
}
```

## 📝 日志配置

可以通过配置控制日志输出：

```javascript
export const LOG_CONFIG = {
  enabled: true,           // 是否启用日志
  level: 'debug',          // 日志级别
  includeRequest: true,    // 是否记录请求
  includeResponse: true,   // 是否记录响应
  maxLogLength: 1000       // 最大日志长度
}
```

## 🔄 添加新的 API

1. 在 `src/api/` 目录下创建新的API文件
2. 使用 `sendSoapRequest` 方法封装具体的业务逻辑
3. 在 `index.js` 中导出新的API模块
4. 在 `config.js` 中添加相关配置

示例：

```javascript
// src/api/patientQuery.js
import { sendSoapRequest } from './request.js'

export async function queryPatient(patientId) {
  const message = `<Request><PatientId>${patientId}</PatientId></Request>`
  return await sendSoapRequest('MES0001', message)
}
```

## 🚨 注意事项

1. **SSL证书**: 开发环境默认忽略SSL证书验证，生产环境需要有效证书
2. **超时设置**: 默认30秒超时，可根据实际情况调整
3. **错误重试**: 可以在配置中启用自动重试机制
4. **数据缓存**: 支持响应数据缓存，减少重复请求
5. **日志记录**: 生产环境建议关闭详细日志以提高性能

## 🧪 测试和验证

### 运行API测试

```javascript
import apiTest from '@/api/test'

// 运行所有测试
const results = await apiTest.runAllTests()
console.log(`测试结果: ${results.passed}/${results.total} 通过`)
```

### 使用演示组件

项目中包含了一个完整的演示组件 `PriceQueryDemo.vue`，展示了所有API功能的使用方法。

```vue
<template>
  <PriceQueryDemo />
</template>

<script>
import PriceQueryDemo from '@/components/PriceQueryDemo.vue'

export default {
  components: {
    PriceQueryDemo
  }
}
</script>
```

## 📞 技术支持

如有问题，请查看：
1. 控制台日志输出
2. 网络请求详情
3. 服务器响应内容
4. API文档和示例代码
5. 运行测试套件检查API连通性

## ✅ 完成清单

- [x] 安装axios HTTP请求库
- [x] 创建通用request.js封装SOAP XML请求
- [x] 实现统一的请求头设置和SOAP信封格式
- [x] 添加错误处理和响应解析功能
- [x] 支持动态配置action和message参数
- [x] 创建物价查询API (MES0061)作为示例
- [x] 实现HTTPS请求和SSL证书处理
- [x] 提供完整的配置管理系统
- [x] 创建使用示例和测试套件
- [x] 提供Vue组件演示
- [x] 编写详细的文档说明
